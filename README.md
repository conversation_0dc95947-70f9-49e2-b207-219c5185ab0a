# SonarQube Scanner CLI with Corporate Certificates

This Docker image extends the official SonarQube Scanner CLI with support for corporate certificates and Node.js/npm.

## Base Image
- **sonar-scanner-cli**: 11.4
- **OS**: Amazon Linux (from base image)

## Included Software

### SonarQube Scanner
- Version: 11.4 (from base image)

### Node.js & npm
- **Node.js**: v22.x (pre-installed in base image)
- **npm**: Latest version (installed via dnf)

## Corporate Certificates

The image includes support for corporate certificates that are automatically installed as trusted CA certificates:

- `cert-1.crt`
- `certnew.cer`
- `proxycert.cer`
- `root-cert.cer`

These certificates are:
1. Copied from the local `certificates/` directory
2. Installed to `/etc/pki/ca-trust/source/anchors/`
3. Added to the system trust store via `update-ca-trust`

## Usage

### Building the Image
```bash
docker build -t sonar-scanner-custom .
```

### Running the Scanner
```bash
docker run --rm -v $(pwd):/usr/src sonar-scanner-custom
```

### With SonarQube Properties
```bash
docker run --rm \
  -v $(pwd):/usr/src \
  -e SONAR_HOST_URL=https://your-sonarqube-server \
  -e SONAR_LOGIN=your-token \
  sonar-scanner-custom
```

## Prerequisites

Before building, ensure you have the required certificates in the `certificates/` directory:
```
certificates/
├── cert-1.crt
├── certnew.cer
├── proxycert.cer
└── root-cert.cer
```

## Notes

- The image runs as root user to install certificates and packages
- Corporate certificates enable HTTPS connections to internal services
- npm is available for JavaScript/TypeScript project analysis

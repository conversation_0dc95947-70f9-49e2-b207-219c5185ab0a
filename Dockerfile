FROM sonarsource/sonar-scanner-cli:11.4

# copy required certs
COPY certificates/ /certificates/

# configure certificates for Amazon Linux
RUN set -eux; \
	# add <PERSON>chser root CA to os certificates
	cp /certificates/cert-1.crt  /etc/pki/ca-trust/source/anchors/cert-1.crt; \
	cp /certificates/certnew.cer  /etc/pki/ca-trust/source/anchors/certnew.crt; \
    cp /certificates/proxycert.cer  /etc/pki/ca-trust/source/anchors/proxycert.crt; \
    cp /certificates/root-cert.cer  /etc/pki/ca-trust/source/anchors/root-cert.crt; \
	update-ca-trust

# Install nodejs and npm (Note: nodejs22 is already installed in the base image)
RUN set -eux; \
    dnf install -y npm;

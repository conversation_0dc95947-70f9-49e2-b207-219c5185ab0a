FROM sonarsource/sonar-scanner-cli:11.4

# copy required certs
COPY certificates/ /certificates/

# configure certificates for Amazon Linux
USER root
RUN set -eux; \
	# add company root CA to os certificates
	cp /certificates/cert-1.crt  /etc/pki/ca-trust/source/anchors/cert-1.crt; \
	cp /certificates/certnew.cer  /etc/pki/ca-trust/source/anchors/certnew.cer; \
    cp /certificates/proxycert.cer  /etc/pki/ca-trust/source/anchors/proxycert.cer; \
    cp /certificates/root-cert.cer  /etc/pki/ca-trust/source/anchors/root-cert.cer; \
	update-ca-trust

# Install nodejs and npm (Note: nodejs22 is already installed in the base image)
RUN set -eux; \
    dnf install -y npm;
